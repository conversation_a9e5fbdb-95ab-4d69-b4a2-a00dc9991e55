#!/bin/bash

# 训练脚本 - 使用合并后的TrainDiffusionUnetHybridPointcloudWorkspace配置
# 基于官方权重兼容的配置文件

echo "=========================================="
echo "开始训练 - Hybrid Pointcloud Workspace"
echo "=========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export WANDB_SILENT=True
export HYDRA_FULL_ERROR=1

# 检查配置文件是否存在
CONFIG_FILE="diffusion_policy_3d/config/train_diffusion_unet_hybrid_pointcloud_workspace.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 配置文件不存在: $CONFIG_FILE"
    exit 1
fi

echo "✅ 使用配置文件: $CONFIG_FILE"

# 数据路径配置
DATASET_PATH="/home/<USER>/code/company/torqueidp3/data/raw_pour_converted"
if [ ! -d "$DATASET_PATH" ]; then
    echo "❌ 数据集路径不存在: $DATASET_PATH"
    echo "请检查数据集路径或修改脚本中的DATASET_PATH变量"
    exit 1
fi

echo "✅ 数据集路径: $DATASET_PATH"

# 训练命令 - 使用合并后的配置文件
python train.py \
    --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    \
    `# 数据配置` \
    task.dataset.zarr_path=$DATASET_PATH \
    task.dataset.seed=42 \
    task.dataset.val_ratio=0.0 \
    task.dataset.max_train_episodes=90 \
    \
    `# 训练配置 - 使用官方种子0` \
    training.seed=0 \
    training.device=cuda:0 \
    training.num_epochs=1500 \
    training.resume=True \
    training.debug=False \
    \
    `# 数据加载配置` \
    dataloader.batch_size=64 \
    dataloader.num_workers=8 \
    \
    `# 检查点配置` \
    checkpoint.save_ckpt=True \
    checkpoint.topk.k=5 \
    checkpoint.save_last_ckpt=True \
    \
    `# 日志配置` \
    exp_name=gr1_dex-3d-hybrid-pointcloud-seed0 \
    logging.mode=offline \
    logging.project=gr1_dex-3d-hybrid-pointcloud \
    \
    `# 训练频率配置` \
    training.checkpoint_every=50 \
    training.val_every=50 \
    training.sample_every=5

echo "=========================================="
echo "训练命令已启动"
echo "关键配置："
echo "  - Workspace: TrainDiffusionUnetHybridPointcloudWorkspace"
echo "  - 配置文件: train_diffusion_unet_hybrid_pointcloud_workspace.yaml"
echo "  - 训练种子: 0 (与官方一致)"
echo "  - 数据种子: 42"
echo "  - 验证集比例: 0.0"
echo "  - 最大训练episodes: 90"
echo "  - 批次大小: 64"
echo "  - 学习率: 1e-4 (配置文件中设置)"
echo "  - 训练epochs: 1500"
echo "  - 保存top-5模型"
echo "  - 数据集: $DATASET_PATH"
echo "=========================================="
