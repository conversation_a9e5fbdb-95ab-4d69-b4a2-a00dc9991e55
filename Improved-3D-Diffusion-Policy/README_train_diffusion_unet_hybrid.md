# TrainDiffusionUnetHybridPointcloudWorkspace 使用说明

## 概述

`train_diffusion_unet_hybrid_pointcloud_workspace.py` 是基于官方 `idp3_workspace.py` 创建的训练工作空间，完全兼容官方权重文件，支持从权重文件恢复训练。

## 主要特性

- ✅ **完全兼容官方权重**: 可以加载和使用官方预训练模型
- ✅ **基于官方实现**: 训练循环与官方 `idp3_workspace.py` 保持一致
- ✅ **描述性命名**: 文件名清楚表达功能用途
- ✅ **配置文件合并**: 整合了 `idp3_official_aligned.yaml` 的所有配置

## 文件结构

```
diffusion_policy_3d/
├── workspace/
│   └── train_diffusion_unet_hybrid_pointcloud_workspace.py  # 主要工作空间文件
├── config/
│   └── train_diffusion_unet_hybrid_pointcloud_workspace.yaml  # 配置文件
└── test_train_diffusion_unet_hybrid.py  # 测试脚本
```

## 配置说明

### 关键配置项

```yaml
# 目标工作空间类
_target_: diffusion_policy_3d.workspace.train_diffusion_unet_hybrid_pointcloud_workspace.TrainDiffusionUnetHybridPointcloudWorkspace

# 实验名称
exp_name: "gr1_dex-3d-idp3-pour-aligned"

# 训练配置
training:
  seed: 0  # 匹配官方种子
  use_ema: True
  num_epochs: 1500
  batch_size: 64

# 检查点配置
checkpoint:
  save_ckpt: True
  topk:
    k: 5  # 保存top-5模型
```

### 与官方的差异

| 配置项 | 官方 | 合并后 | 说明 |
|--------|------|--------|------|
| `checkpoint.topk.k` | 0 | 5 | 保存top-5模型更实用 |
| `logging.tags` | `[train_diffusion_unet_hybrid, dexdeform]` | `[train_diffusion_unet_hybrid, dexdeform, pointcloud, official_aligned]` | 添加更多标签 |
| `exp_name` | `gr1_dex-3d-idp3-pour-aligned` | 同左 | 保持一致 |

## 使用方法

### 1. 基本训练

```bash
python train.py --config-name=train_diffusion_unet_hybrid_pointcloud_workspace
```

### 2. 从权重文件恢复训练

```bash
python train.py --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    training.resume=True
```

### 3. 自定义参数

```bash
python train.py --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    training.seed=42 \
    dataloader.batch_size=32 \
    training.num_epochs=2000
```

## 测试验证

运行测试脚本验证配置：

```bash
python test_train_diffusion_unet_hybrid.py
```

预期输出：
```
✅ Successfully created workspace from checkpoint
✅ Successfully loaded model
✅ Model has 68,797,499 parameters
✅ All tests passed! The workspace is ready to use.
```

## 权重兼容性

该工作空间完全兼容以下权重文件：
- 官方预训练权重
- 使用 `idp3_workspace.py` 训练的权重
- 使用本工作空间训练的权重

### 权重文件结构

权重文件包含以下组件：
```python
{
    'cfg': OmegaConf配置,
    'state_dicts': {
        'model': 主模型参数,
        'ema_model': EMA模型参数,
        'optimizer': 优化器状态
    },
    'pickles': {
        'global_step': 训练步数,
        'epoch': 训练轮数,
        '_output_dir': 输出目录
    }
}
```

## 故障排除

### 1. HydraConfig错误

如果遇到 `HydraConfig was not set` 错误：
- 确保使用 `python train.py` 而不是直接运行workspace文件
- 检查配置文件路径是否正确

### 2. 权重加载失败

如果权重加载失败：
- 检查权重文件路径
- 确认权重文件完整性
- 验证配置与权重文件的兼容性

### 3. 内存不足

如果训练时内存不足：
- 减少 `dataloader.batch_size`
- 减少 `dataloader.num_workers`
- 设置 `dataloader.persistent_workers=false`

## 开发说明

### 基于官方实现

本工作空间基于官方 `idp3_workspace.py` 实现，主要修改：
1. 添加了更好的错误处理
2. 改进了 `get_model` 方法的兼容性
3. 保持了与官方权重的完全兼容性

### 扩展功能

如需添加新功能，建议：
1. 保持与官方实现的兼容性
2. 在 `run()` 方法中添加新的训练逻辑
3. 更新配置文件以支持新参数
4. 添加相应的测试用例

## 更新日志

- **v1.0**: 初始版本，基于官方 `idp3_workspace.py`
- **v1.1**: 合并 `idp3_official_aligned.yaml` 配置
- **v1.2**: 添加完整的测试验证和文档
