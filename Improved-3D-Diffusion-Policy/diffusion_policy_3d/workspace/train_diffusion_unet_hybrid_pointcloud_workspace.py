"""
TrainDiffusionUnetHybridPointcloudWorkspace
Compatible with official weights that use multi-camera setup
Based on official iDP3Workspace implementation
"""

if __name__ == "__main__":
    import sys
    import os
    import pathlib
    ROOT_DIR = str(pathlib.Path(__file__).parent.parent.parent)
    sys.path.append(ROOT_DIR)
    os.chdir(ROOT_DIR)

import os
import hydra
import torch
from omegaconf import OmegaConf
import pathlib
from torch.utils.data import DataLoader
import copy
import random
import wandb
import tqdm
import numpy as np
from termcolor import cprint
import shutil
import time
from diffusion_policy_3d.workspace.base_workspace import BaseWorkspace
from diffusion_policy_3d.policy.diffusion_pointcloud_policy import DiffusionPointcloudPolicy
from diffusion_policy_3d.common.checkpoint_util import TopKCheckpointManager
from diffusion_policy_3d.common.json_logger import JsonLogger
from diffusion_policy_3d.common.pytorch_util import dict_apply, optimizer_to
from diffusion_policy_3d.model.diffusion.ema_model import EMAModel
from diffusion_policy_3d.model.common.lr_scheduler import get_scheduler

OmegaConf.register_new_resolver("eval", eval, replace=True)

class TrainDiffusionUnetHybridPointcloudWorkspace(BaseWorkspace):
    include_keys = ['global_step', 'epoch', 'best_score']

    def __init__(self, cfg: OmegaConf, output_dir=None):
        super().__init__(cfg, output_dir=output_dir)
        
        # set seed
        seed = cfg.training.seed
        torch.manual_seed(seed)
        np.random.seed(seed)
        random.seed(seed)

        # configure model
        self.model: DiffusionPointcloudPolicy = hydra.utils.instantiate(cfg.policy)

        self.ema_model: DiffusionPointcloudPolicy = None
        if cfg.training.use_ema:
            try:
                self.ema_model = copy.deepcopy(self.model)
            except: # minkowski engine could not be copied. recreate it
                self.ema_model = hydra.utils.instantiate(cfg.policy)

        # configure training state
        self.optimizer = hydra.utils.instantiate(
            cfg.optimizer, params=self.model.parameters())

        # configure training state
        self.global_step = 0
        self.epoch = 0
        self.best_score = float('-inf')  # 跟踪最佳验证分数

    def run(self):
        cfg = copy.deepcopy(self.cfg)
        
        # configure dataset
        dataset = hydra.utils.instantiate(cfg.task.dataset)
        train_dataloader = DataLoader(dataset, **cfg.dataloader)
        normalizer = dataset.get_normalizer()

        # configure validation dataset
        val_dataset = dataset.get_validation_dataset()
        val_dataloader = DataLoader(val_dataset, **cfg.val_dataloader)

        self.model.set_normalizer(normalizer)
        if cfg.training.use_ema:
            self.ema_model.set_normalizer(normalizer)

        # configure lr scheduler
        lr_scheduler = get_scheduler(
            cfg.training.lr_scheduler,
            optimizer=self.optimizer,
            num_warmup_steps=cfg.training.lr_warmup_steps,
            num_training_steps=(
                len(train_dataloader) * cfg.training.num_epochs) \
                // cfg.training.gradient_accumulate_every,
            # pytorch assumes stepping LRScheduler every epoch
            # however huggingface diffusers steps it every batch
            last_epoch=self.global_step-1
        )

        # configure ema
        ema: EMAModel = None
        if cfg.training.use_ema:
            ema = hydra.utils.instantiate(
                cfg.ema,
                model=self.ema_model)

        cfg.logging.name = str(cfg.logging.name)
        cprint("-----------------------------", "yellow")
        cprint(f"[WandB] group: {cfg.logging.group}", "yellow")
        cprint(f"[WandB] name: {cfg.logging.name}", "yellow")
        cprint("-----------------------------", "yellow")
        # configure logging
        wandb_run = wandb.init(
            dir=str(self.output_dir),
            config=OmegaConf.to_container(cfg, resolve=True),
            **cfg.logging
        )
        wandb.config.update(
            {
                "output_dir": self.output_dir,
            }
        )

        # configure checkpoint
        topk_manager = TopKCheckpointManager(
            save_dir=os.path.join(self.output_dir, 'checkpoints'),
            **cfg.checkpoint.topk
        )

        # device transfer
        device = torch.device(cfg.training.device)
        self.model.to(device)
        if self.ema_model is not None:
            self.ema_model.to(device)
        optimizer_to(self.optimizer, device)

        # save batch for sampling
        train_sampling_batch = None

        # training loop
        log_path = os.path.join(self.output_dir, 'logs.json.txt')
        with JsonLogger(log_path) as json_logger:
            for local_epoch_idx in tqdm.tqdm(range(cfg.training.num_epochs)):
                step_log = dict()
                # ========= train for this epoch ==========
                train_losses = list()
                for batch_idx, batch in enumerate(train_dataloader):
                    # device transfer
                    batch = dict_apply(batch, lambda x: x.to(device, non_blocking=True) if isinstance(x, torch.Tensor) else x)
                    if train_sampling_batch is None:
                        train_sampling_batch = batch
                
                    # compute loss
                    raw_loss, loss_dict = self.model.compute_loss(batch)
                    loss = raw_loss / cfg.training.gradient_accumulate_every
                    loss.backward()

                    # step optimizer
                    if self.global_step % cfg.training.gradient_accumulate_every == 0:
                        self.optimizer.step()
                        self.optimizer.zero_grad()
                        lr_scheduler.step()
                    
                    # update ema
                    if cfg.training.use_ema:
                        ema.step(self.model)

                    # logging
                    raw_loss_cpu = raw_loss.item()
                    train_losses.append(raw_loss_cpu)
                    step_log = {
                        'train_loss': raw_loss_cpu,
                        'global_step': self.global_step,
                        'epoch': self.epoch,
                        'lr': lr_scheduler.get_last_lr()[0]
                    }

                    step_log.update(loss_dict)

                    is_last_batch = (batch_idx == (len(train_dataloader)-1))
                    if not is_last_batch:
                        # log of last step is combined with validation and rollout
                        wandb_run.log(step_log, step=self.global_step)
                        json_logger.log(step_log)
                        self.global_step += 1

                    # at the last batch, no need to increase global_step
                    # since global_step will be incremented at the end of epoch

                # at the end of each epoch
                # replace train_loss with epoch average
                train_loss = np.mean(train_losses)
                step_log['train_loss'] = train_loss

                # ========= eval for this epoch ==========
                policy = self.model
                if cfg.training.use_ema:
                    policy = self.ema_model
                policy.eval()

                # run validation - 确保每次checkpoint前都有最新的验证分数
                val_loss = None
                if (self.epoch % cfg.training.val_every) == 0 or (self.epoch % cfg.training.checkpoint_every) == 0:
                    with torch.no_grad():
                        train_losses = list()
                        for batch_idx, batch in enumerate(train_dataloader):
                            batch = dict_apply(batch, lambda x: x.to(device, non_blocking=True) if isinstance(x, torch.Tensor) else x)
                            obs_dict = batch['obs']
                            gt_action = batch['action']
                            result = policy.predict_action(obs_dict)
                            pred_action = result['action_pred']
                            mse = torch.nn.functional.mse_loss(pred_action, gt_action)
                            train_losses.append(mse.item())
                            if (cfg.training.max_train_steps is not None) \
                                and batch_idx >= (cfg.training.max_train_steps-1):
                                break
                        val_loss = np.mean(train_losses)  # 使用平均值而不是总和
                        current_score = -val_loss  # 负损失作为分数，越高越好

                        # 更新最佳分数
                        is_best = current_score > self.best_score
                        if is_best:
                            self.best_score = current_score

                        # log epoch average validation loss
                        step_log['train_action_mse_error'] = val_loss
                        step_log['test_mean_score'] = current_score
                        step_log['best_score'] = self.best_score
                        step_log['is_best'] = is_best

                        cprint(f"val loss: {val_loss:.7f}, score: {current_score:.7f}, best: {self.best_score:.7f} {'🎯' if is_best else ''}", "cyan")

                # checkpoint - 只在有验证分数时保存基于性能的模型
                if (self.epoch % cfg.training.checkpoint_every) == 0 and cfg.checkpoint.save_ckpt:
                    # 总是保存latest checkpoint
                    if cfg.checkpoint.save_last_ckpt:
                        self.save_checkpoint()
                    if cfg.checkpoint.save_last_snapshot:
                        self.save_snapshot()

                    # 只有当有验证分数时才保存基于性能的模型
                    if 'test_mean_score' in step_log:
                        # sanitize metric names
                        metric_dict = dict()
                        for key, value in step_log.items():
                            new_key = key.replace('/', '_')
                            metric_dict[new_key] = value

                        topk_ckpt_path = topk_manager.get_ckpt_path(metric_dict)

                        if topk_ckpt_path is not None:
                            self.save_checkpoint(path=topk_ckpt_path)
                            cprint(f"Performance-based checkpoint saved: {topk_ckpt_path}", "green")
                        else:
                            cprint("Performance checkpoint not saved (not in top-k)", "yellow")
                    else:
                        cprint("No validation score available, only latest checkpoint saved", "yellow")
                # ========= eval end for this epoch ==========
                policy.train()

                # end of epoch
                # log of last step is combined with validation and rollout
                wandb_run.log(step_log, step=self.global_step)
                json_logger.log(step_log)
                
                self.global_step += 1
                self.epoch += 1
                del step_log

        # stop wandb run
        wandb_run.finish()
    
    def get_model(self, ckpt_path=None):
        cfg = copy.deepcopy(self.cfg)

        if ckpt_path is None:
            tag = "latest"
            # tag = "best"
            try:
                lastest_ckpt_path = self.get_checkpoint_path(tag=tag)

                if lastest_ckpt_path.is_file():
                    cprint(f"Resuming from checkpoint {lastest_ckpt_path}", 'magenta')
                    self.load_checkpoint(path=lastest_ckpt_path)
            except ValueError:
                # HydraConfig not set, model already loaded from checkpoint
                cprint("Model already loaded from checkpoint", 'magenta')
                pass
        else:
            if ckpt_path.is_file():
                cprint(f"Resuming from checkpoint {ckpt_path}", 'magenta')
                self.load_checkpoint(path=ckpt_path)
            else:
                raise ValueError(f"Checkpoint file not found: {ckpt_path}")

        policy = self.model
        if cfg.training.use_ema:
            policy = self.ema_model
        policy.eval()

        return policy

@hydra.main(
    config_path=str(pathlib.Path(__file__).parent.parent.joinpath("config")),
    config_name=pathlib.Path(__file__).stem,
    version_base=None)
def main(cfg):
    workspace = TrainDiffusionUnetHybridPointcloudWorkspace(cfg)
    workspace.run()

if __name__ == "__main__":
    main()
