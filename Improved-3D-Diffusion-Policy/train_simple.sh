#!/bin/bash

# 简化训练脚本 - 使用配置文件中的默认值
# 只需要指定数据集路径

echo "=========================================="
echo "开始训练 - 简化版本"
echo "=========================================="

# 激活conda环境
source ~/miniconda3/etc/profile.d/conda.sh
conda activate idp3

# 设置环境变量
export CUDA_VISIBLE_DEVICES=0
export HYDRA_FULL_ERROR=1

# 数据路径配置
DATASET_PATH="/home/<USER>/code/company/torqueidp3/data/raw_pour_converted"

# 检查数据集是否存在
if [ ! -d "$DATASET_PATH" ]; then
    echo "❌ 数据集路径不存在: $DATASET_PATH"
    echo "请检查数据集路径或修改脚本中的DATASET_PATH变量"
    exit 1
fi

echo "✅ 数据集路径: $DATASET_PATH"
echo "✅ 使用配置文件: train_diffusion_unet_hybrid_pointcloud_workspace.yaml"

# 简化的训练命令 - 大部分参数使用配置文件默认值
python train.py \
    --config-name=train_diffusion_unet_hybrid_pointcloud_workspace \
    task.dataset.zarr_path=$DATASET_PATH

echo "=========================================="
echo "训练已启动 - 使用配置文件默认值"
echo "关键配置 (来自配置文件):"
echo "  - Workspace: TrainDiffusionUnetHybridPointcloudWorkspace"
echo "  - 训练种子: 0"
echo "  - 批次大小: 64"
echo "  - 学习率: 1e-4"
echo "  - 训练epochs: 1500"
echo "  - 使用EMA: True"
echo "  - 保存top-5模型"
echo "  - 数据集: $DATASET_PATH"
echo "=========================================="
