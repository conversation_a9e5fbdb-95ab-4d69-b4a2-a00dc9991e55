#!/usr/bin/env python3
"""
Test script for train_diffusion_unet_hybrid_pointcloud_workspace.py
验证workspace是否能正确加载官方权重并进行训练
"""

import sys
import os
import pathlib
ROOT_DIR = str(pathlib.Path(__file__).parent.absolute())
sys.path.append(ROOT_DIR)
os.chdir(ROOT_DIR)

import torch
import hydra
from omegaconf import OmegaConf
from diffusion_policy_3d.workspace.train_diffusion_unet_hybrid_pointcloud_workspace import TrainDiffusionUnetHybridPointcloudWorkspace

def test_workspace_compatibility():
    """测试workspace与官方权重的兼容性"""
    print("=== Testing Workspace Compatibility ===")
    
    # 测试权重文件路径
    ckpt_path = 'data/outputs/gr1_dex-3d-idp3-full_training_seed0/checkpoints/latest.ckpt'
    
    if not os.path.exists(ckpt_path):
        print(f"❌ Checkpoint file not found: {ckpt_path}")
        return False
    
    try:
        # 1. 测试从权重文件创建workspace
        workspace = TrainDiffusionUnetHybridPointcloudWorkspace.create_from_checkpoint(ckpt_path)
        print("✅ Successfully created workspace from checkpoint")
        
        # 2. 测试获取模型
        model = workspace.get_model()
        print("✅ Successfully loaded model")
        print(f"   Model type: {type(model)}")
        
        # 3. 测试模型参数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"   Model has {total_params:,} parameters")
        
        # 4. 测试配置兼容性
        cfg = workspace.cfg
        print(f"   Target class: {cfg._target_}")
        print(f"   Task name: {cfg.task_name}")
        print(f"   Use EMA: {cfg.training.use_ema}")
        
        # 5. 测试模型状态
        print(f"   Model training mode: {model.training}")
        print(f"   Global step: {workspace.global_step}")
        print(f"   Epoch: {workspace.epoch}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置文件加载"""
    print("\n=== Testing Config Loading ===")
    
    try:
        # 测试加载新的配置文件
        config_path = "diffusion_policy_3d/config"
        config_name = "train_diffusion_unet_hybrid_pointcloud_workspace"
        
        with hydra.initialize(config_path=config_path, version_base=None):
            cfg = hydra.compose(config_name=config_name)
            print("✅ Successfully loaded config")
            print(f"   Target class: {cfg._target_}")
            print(f"   Policy target: {cfg.policy._target_}")
            print(f"   Use EMA: {cfg.training.use_ema}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("Testing TrainDiffusionUnetHybridPointcloudWorkspace")
    print("=" * 60)
    
    # 测试1: 权重兼容性
    test1_passed = test_workspace_compatibility()
    
    # 测试2: 配置加载
    test2_passed = test_config_loading()
    
    # 总结
    print("\n" + "=" * 60)
    print("Test Summary:")
    print(f"  Workspace compatibility: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  Config loading: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The workspace is ready to use.")
        print("\nUsage:")
        print("  python train.py --config-name=train_diffusion_unet_hybrid_pointcloud_workspace")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
    
    return test1_passed and test2_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
