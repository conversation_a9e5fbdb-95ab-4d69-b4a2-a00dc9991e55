# 训练脚本使用说明

## 快速开始

```bash
# 直接运行训练脚本
./train_hybrid_pointcloud.sh
```

## 脚本特性

✅ **完全兼容官方权重** - 68.8M参数，与官方实现完全一致  
✅ **自动错误检查** - 验证配置文件和数据集路径  
✅ **详细配置输出** - 清楚显示所有训练参数  
✅ **断点续训** - 支持从权重文件恢复训练  
✅ **保存最佳模型** - 自动保存top-5最佳模型  

## 关键配置

- **Workspace**: `TrainDiffusionUnetHybridPointcloudWorkspace`
- **配置文件**: `train_diffusion_unet_hybrid_pointcloud_workspace.yaml`
- **训练种子**: 0 (与官方一致)
- **批次大小**: 64
- **学习率**: 1e-4
- **训练轮数**: 1500
- **数据集**: `/home/<USER>/code/company/torqueidp3/data/raw_pour_converted`

## 输出

训练过程中会在 `data/outputs/` 目录下创建输出文件夹，包含：
- 检查点文件 (`checkpoints/`)
- 训练日志 (`logs.json.txt`)
- WandB日志 (`wandb/`)

## 注意事项

1. 确保数据集路径正确
2. 确保有足够的GPU内存 (建议24GB+)
3. 训练过程较长，建议使用 `screen` 或 `tmux`
