# 训练脚本使用说明

## 快速开始

```bash
# 直接运行训练脚本
./train_hybrid_pointcloud.sh
```

## 脚本特性

✅ **完全兼容官方权重** - 68.8M参数，与官方实现完全一致
✅ **显式参数配置** - 所有关键参数通过命令行明确指定
✅ **自动错误检查** - 验证配置文件和数据集路径
✅ **详细配置输出** - 清楚显示所有训练参数和模型架构
✅ **断点续训** - 支持从权重文件恢复训练
✅ **保存最佳模型** - 自动保存top-5最佳模型

## 详细配置 (183行完整脚本)

### 核心配置
- **Workspace**: `TrainDiffusionUnetHybridPointcloudWorkspace`
- **配置文件**: `train_diffusion_unet_hybrid_pointcloud_workspace.yaml`
- **训练种子**: 0 (与官方一致)
- **数据种子**: 42
- **训练轮数**: 1500

### 模型架构
- **总参数**: ~68.8M (与官方权重兼容)
- **扩散步数**: 50 (训练) / 10 (推理)
- **下采样维度**: [256, 512, 1024]
- **点云编码器**: 128维输出, 4096点
- **EMA衰减**: 0.75

### 训练参数
- **批次大小**: 64
- **学习率**: 1e-4
- **优化器**: AdamW (betas=[0.95,0.999])
- **学习率调度**: cosine with 300 warmup steps
- **检查点**: 每100轮保存
- **验证**: 每100轮评估

## 输出

训练过程中会在 `data/outputs/` 目录下创建输出文件夹，包含：
- 检查点文件 (`checkpoints/`)
- 训练日志 (`logs.json.txt`)
- WandB日志 (`wandb/`)

## 注意事项

1. 确保数据集路径正确
2. 确保有足够的GPU内存 (建议24GB+)
3. 训练过程较长，建议使用 `screen` 或 `tmux`
4. 所有参数都通过命令行显式指定，便于调试和修改
